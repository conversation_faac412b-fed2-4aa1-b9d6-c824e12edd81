<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid Overlay Test Page</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .instructions {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .drop-zone {
            border: 3px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background-color: #fafafa;
            transition: all 0.3s ease;
        }
        .drop-zone.dragover {
            border-color: #007bff;
            background-color: #e8f4fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Artist Grid Overlay Test Page</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Make sure you've enabled "Allow access to file URLs" for the extension</li>
                <li>Click the extension icon in the toolbar</li>
                <li>Click "Toggle Grid" to show/hide the grid overlay</li>
                <li>Adjust grid settings using the sliders</li>
                <li>Try dropping an image into the drop zone below</li>
            </ol>
        </div>

        <div class="image-container">
            <h3>Sample Image:</h3>
            <img src="file:///Users/<USER>/Pictures/att.CDL3Z328xvbqc4zPwBTyeMRiMXhKuEoyOdzXtwRjJHE.jpg" 
                 alt="Test Image" 
                 id="test-image">
        </div>

        <div class="drop-zone" id="dropZone">
            <h3>📁 Drop Image Here</h3>
            <p>Drag and drop an image file here to test the grid overlay</p>
            <p><small>Supported formats: JPG, PNG, GIF, WebP</small></p>
        </div>

        <div id="dropped-image-container" style="display: none;">
            <h3>Dropped Image:</h3>
            <img id="dropped-image" alt="Dropped Image">
        </div>
    </div>

    <script>
        // Handle drag and drop
        const dropZone = document.getElementById('dropZone');
        const droppedImageContainer = document.getElementById('dropped-image-container');
        const droppedImage = document.getElementById('dropped-image');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        droppedImage.src = e.target.result;
                        droppedImageContainer.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            }
        });

        // Log when page loads to help with debugging
        console.log('Test page loaded. Extension should be able to inject content script here.');
    </script>
</body>
</html>
