<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artist Grid Overlay</title>
    <style>
        body {
            width: 250px;
            padding: 15px;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.9;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .btn.primary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            font-weight: 600;
        }

        .btn.primary:hover {
            background: white;
            color: #5a67d8;
        }

        .status {
            margin-top: 15px;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
        }

        .keyboard-hint {
            margin-top: 10px;
            font-size: 11px;
            text-align: center;
            opacity: 0.7;
            font-style: italic;
        }

        .error {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid rgba(231, 76, 60, 0.3);
            color: #ff6b6b;
            padding: 12px;
            border-radius: 6px;
            font-size: 11px;
            line-height: 1.4;
            margin-top: 10px;
            display: none;
            max-height: 120px;
            overflow-y: auto;
        }

        .error code {
            background: rgba(0, 0, 0, 0.2);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 10px;
        }

        .error strong {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Artist Grid</h1>
        <p>Drawing assistance overlay</p>
    </div>

    <div class="button-group">
        <button id="togglePanel" class="btn primary">
            📋 Open Control Panel
        </button>
        
        <button id="toggleGrid" class="btn">
            ⚡ Toggle Grid
        </button>
    </div>

    <div class="keyboard-hint">
        💡 Tip: Use Ctrl+Shift+G to toggle panel
    </div>

    <div id="status" class="status" style="display: none;">
        Ready
    </div>

    <div id="error" class="error">
        Failed to communicate with page. Try refreshing the page.
    </div>

    <script src="popup.js"></script>
</body>
</html>
