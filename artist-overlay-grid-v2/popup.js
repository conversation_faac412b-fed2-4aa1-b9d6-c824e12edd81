// Popup script for Artist Grid Overlay

/**
 * Popup controller for Artist Grid Overlay extension
 */
class PopupController {
    constructor() {
        this.tab = null;
        this.isContentScriptReady = false;
        this.init();
    }

    async init() {
        try {
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.tab = tab;

            // Check if content script is ready
            await this.checkContentScript();

            // Set up event listeners
            this.setupEventListeners();

            // Load and display current settings
            await this.loadCurrentSettings();

            this.showStatus('Ready', 'success');
        } catch (error) {
            this.showError('Failed to initialize popup: ' + error.message);
        }
    }

    async checkContentScript() {
        try {
            // Check if we're on a file URL
            if (this.tab.url.startsWith('file://')) {
                return await this.checkFileURLContentScript();
            }

            const response = await chrome.tabs.sendMessage(this.tab.id, { action: 'ping' });
            this.isContentScriptReady = response && response.success;
        } catch (error) {
            this.isContentScriptReady = false;
        }
    }

    async checkFileURLContentScript() {
        try {
            // For file URLs, we need to check if the extension has file access permission
            const hasFileAccess = await this.checkFileURLPermission();
            if (!hasFileAccess) {
                throw new Error('FILE_ACCESS_DENIED');
            }

            // Try to communicate with content script with retries
            for (let i = 0; i < 5; i++) {
                try {
                    const response = await chrome.tabs.sendMessage(this.tab.id, { action: 'ping' });
                    if (response && response.success) {
                        this.isContentScriptReady = true;
                        return;
                    }
                } catch (error) {
                    // Content script might still be initializing, wait and retry
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            this.isContentScriptReady = false;
        } catch (error) {
            if (error.message === 'FILE_ACCESS_DENIED') {
                throw error;
            }
            this.isContentScriptReady = false;
        }
    }

    async checkFileURLPermission() {
        try {
            // Try to execute a simple script to test file URL access
            await chrome.scripting.executeScript({
                target: { tabId: this.tab.id },
                func: () => true
            });
            return true;
        } catch (error) {
            // If this fails, it's likely due to missing file URL permission
            return false;
        }
    }

    setupEventListeners() {
        const togglePanelBtn = document.getElementById('togglePanel');
        const toggleGridBtn = document.getElementById('toggleGrid');

        if (!togglePanelBtn || !toggleGridBtn) {
            throw new Error('Required UI elements not found');
        }

        // Toggle control panel
        togglePanelBtn.addEventListener('click', () => this.handleTogglePanel());

        // Toggle grid
        toggleGridBtn.addEventListener('click', () => this.handleToggleGrid());
    }

    async handleTogglePanel() {
        try {
            this.showStatus('Opening panel...', 'loading');

            // Special handling for file URLs
            if (this.tab.url.startsWith('file://')) {
                await this.handleFileURLAction('togglePanel');
                return;
            }

            if (!this.isContentScriptReady) {
                await this.injectContentScript();
            }

            const response = await chrome.tabs.sendMessage(this.tab.id, { action: 'togglePanel' });

            if (response && response.success) {
                this.showStatus('Panel toggled', 'success');
                setTimeout(() => window.close(), 500);
            } else {
                throw new Error('Failed to toggle panel');
            }
        } catch (error) {
            this.showError('Error toggling panel: ' + error.message);
        }
    }

    async handleToggleGrid() {
        try {
            this.showStatus('Toggling grid...', 'loading');

            // Special handling for file URLs
            if (this.tab.url.startsWith('file://')) {
                await this.handleFileURLAction('toggleGrid');
                return;
            }

            if (!this.isContentScriptReady) {
                await this.injectContentScript();
            }

            const response = await chrome.tabs.sendMessage(this.tab.id, { action: 'toggleGrid' });

            if (response && response.success) {
                this.showStatus('Grid toggled', 'success');
                setTimeout(() => window.close(), 500);
            } else {
                throw new Error('Failed to toggle grid');
            }
        } catch (error) {
            this.showError('Error toggling grid: ' + error.message);
        }
    }

    async handleFileURLAction(action) {
        try {
            // Check file URL permission first
            const hasFileAccess = await this.checkFileURLPermission();
            if (!hasFileAccess) {
                throw new Error('FILE_ACCESS_PERMISSION_REQUIRED');
            }

            // Ensure content script is injected and ready
            if (!this.isContentScriptReady) {
                await this.injectContentScript();
            }

            // Try the action with retries
            let response = null;
            for (let i = 0; i < 3; i++) {
                try {
                    response = await chrome.tabs.sendMessage(this.tab.id, { action });
                    if (response && response.success) {
                        break;
                    }
                } catch (error) {
                    if (i === 2) throw error; // Last attempt
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            if (response && response.success) {
                this.showStatus(`${action === 'togglePanel' ? 'Panel' : 'Grid'} toggled`, 'success');
                setTimeout(() => window.close(), 500);
            } else {
                throw new Error(`Failed to ${action}`);
            }
        } catch (error) {
            throw error; // Re-throw to be handled by caller
        }
    }

    async injectContentScript() {
        try {
            this.showStatus('Loading extension...', 'loading');

            // Check for file URL permission first
            if (this.tab.url.startsWith('file://')) {
                const hasFileAccess = await this.checkFileURLPermission();
                if (!hasFileAccess) {
                    throw new Error('FILE_ACCESS_DENIED');
                }
            }

            // Inject content script and CSS
            await chrome.scripting.executeScript({
                target: { tabId: this.tab.id },
                files: ['content.js']
            });

            await chrome.scripting.insertCSS({
                target: { tabId: this.tab.id },
                files: ['overlay.css']
            });

            // Wait longer for file URLs to initialize
            const waitTime = this.tab.url.startsWith('file://') ? 2000 : 500;
            await new Promise(resolve => setTimeout(resolve, waitTime));

            // Check if content script is now ready with retries
            await this.waitForContentScriptReady();

            if (!this.isContentScriptReady) {
                throw new Error('Content script failed to initialize after injection');
            }
        } catch (error) {
            if (error.message === 'FILE_ACCESS_DENIED') {
                throw new Error('FILE_ACCESS_PERMISSION_REQUIRED');
            }
            throw new Error('Failed to inject content script: ' + error.message);
        }
    }

    async waitForContentScriptReady() {
        // Try multiple times to check if content script is ready
        for (let i = 0; i < 10; i++) {
            try {
                // Check if the content script has set the ready flag
                const result = await chrome.scripting.executeScript({
                    target: { tabId: this.tab.id },
                    func: () => window.artistGridOverlayReady
                });

                if (result && result[0] && result[0].result) {
                    // Now try to communicate with the content script
                    const response = await chrome.tabs.sendMessage(this.tab.id, { action: 'ping' });
                    if (response && response.success) {
                        this.isContentScriptReady = true;
                        return;
                    }
                }
            } catch (error) {
                // Continue trying
            }

            await new Promise(resolve => setTimeout(resolve, 300));
        }

        this.isContentScriptReady = false;
    }

    async loadCurrentSettings() {
        try {
            const result = await chrome.storage.sync.get(['artistGridSettings']);
            if (result.artistGridSettings) {
                this.updateButtonStates(result.artistGridSettings);
            }
        } catch (error) {
            console.warn('Could not load settings:', error);
        }
    }

    updateButtonStates(settings) {
        const togglePanelBtn = document.getElementById('togglePanel');
        const toggleGridBtn = document.getElementById('toggleGrid');

        if (settings.panelVisible) {
            togglePanelBtn.textContent = '📋 Close Control Panel';
        } else {
            togglePanelBtn.textContent = '📋 Open Control Panel';
        }

        if (settings.isVisible) {
            toggleGridBtn.textContent = '⚡ Hide Grid';
        } else {
            toggleGridBtn.textContent = '⚡ Show Grid';
        }
    }

    showStatus(message, type = 'info') {
        const statusEl = document.getElementById('status');
        const errorEl = document.getElementById('error');

        if (statusEl) {
            statusEl.textContent = message;
            statusEl.style.display = 'block';
            statusEl.className = `status ${type}`;
        }

        if (errorEl) {
            errorEl.style.display = 'none';
        }
    }

    showError(message) {
        const statusEl = document.getElementById('status');
        const errorEl = document.getElementById('error');

        // Handle specific error types with helpful messages
        let displayMessage = message;
        if (message.includes('FILE_ACCESS_PERMISSION_REQUIRED') || message.includes('FILE_ACCESS_DENIED')) {
            displayMessage = this.getFileAccessErrorMessage();
        } else if (message.includes('Content script failed to initialize')) {
            displayMessage = 'Extension failed to load on this page. Try refreshing the page.';
        }

        if (errorEl) {
            errorEl.innerHTML = displayMessage;
            errorEl.style.display = 'block';
        }

        if (statusEl) {
            statusEl.style.display = 'none';
        }
    }

    getFileAccessErrorMessage() {
        return `
            <strong>File Access Required</strong><br>
            To use this extension on local files:<br>
            1. Go to <code>chrome://extensions/</code><br>
            2. Find "Grid Overlay for Artists"<br>
            3. Enable "Allow access to file URLs"<br>
            4. Refresh this page
        `;
    }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});