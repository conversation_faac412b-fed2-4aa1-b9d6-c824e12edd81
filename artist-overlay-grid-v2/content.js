// Artist Grid Overlay - Content Script (Enhanced for File Support)

/**
 * Artist Grid Overlay class for creating customizable grid overlays on web pages
 * Supports file contexts, drag-and-drop controls, and various grid presets
 */
class ArtistGridOverlay {
  // Constants
  static CONSTANTS = {
    DEBOUNCE_DELAY: 300,
    FILE_LOAD_TIMEOUT: 5000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
    MIN_GRID_SIZE: 10,
    MAX_GRID_SIZE: 200,
    MIN_OFFSET: -100,
    MAX_OFFSET: 100,
    MIN_OPACITY: 0.1,
    MAX_OPACITY: 1.0
  };

  constructor() {
    this.isInitialized = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.isFileContext = null;
    this.eventListeners = new Map(); // Track event listeners for cleanup
    this.debounceTimers = new Map(); // Track debounce timers
    this.settings = this.getDefaultSettings();

    // Bind methods to preserve context
    this.handleMessage = this.handleMessage.bind(this);
    this.handleKeydown = this.handleKeydown.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);

    this.safeInit();
  }

  /**
   * Get default settings configuration
   */
  getDefaultSettings() {
    return {
      gridColor: '#ff0000',
      gridSizeX: 50,
      gridSizeY: 50,
      gridOffsetX: 0,
      gridOffsetY: 0,
      opacity: 0.7,
      isVisible: false,
      panelVisible: false
    };
  }

  /**
   * Safe initialization with error handling
   */
  async safeInit() {
    try {
      // Special handling for file URLs
      if (window.location.protocol === 'file:') {
        await this.initForFileURL();
      } else {
        this.isFileContext = this.detectFileContext();
        await this.init();
      }

      this.setupMessageListener();
      this.setupCleanupHandlers();

      // Signal that initialization is complete
      window.artistGridOverlayReady = true;

    } catch (error) {
      this.logError('Failed to initialize ArtistGridOverlay', error);
      // Still signal ready even if there was an error, so popup can show error
      window.artistGridOverlayReady = true;
      throw error;
    }
  }

  /**
   * Special initialization for file URLs
   */
  async initForFileURL() {
    try {
      // Wait longer for file content to load
      await this.waitForFileContentLoad();

      // Detect file context after content loads
      this.isFileContext = this.detectFileContext();

      // Initialize with file-specific settings
      await this.init();

      // Auto-show panel and grid for file contexts after a delay
      if (this.isFileContext?.isFile) {
        setTimeout(() => {
          // Auto-show grid for file contexts
          if (!this.settings.isVisible) {
            console.log('[ArtistGridOverlay] Auto-showing grid for file context');
            this.settings.isVisible = true;
            if (this.overlay) {
              this.overlay.classList.add('active');
            }
            this.saveSettings();
          }

          // Auto-show panel
          if (!this.settings.panelVisible) {
            this.showPanel();
          }
        }, 1500);
      }

    } catch (error) {
      this.logError('Failed to initialize for file URL', error);
      throw error;
    }
  }

  /**
   * Wait for file content to load with extended timeout for local files
   */
  async waitForFileContentLoad() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('File content load timeout'));
      }, 10000); // Longer timeout for file URLs

      const checkContent = () => {
        // Check if document is ready and has content
        if (document.readyState === 'complete') {
          // For images, check if image element exists and is loaded
          const img = document.querySelector('img');
          if (img) {
            if (img.complete && img.naturalWidth > 0) {
              clearTimeout(timeout);
              resolve();
              return;
            }
          } else {
            // For other file types, just wait for document ready
            clearTimeout(timeout);
            resolve();
            return;
          }
        }

        // Continue checking
        setTimeout(checkContent, 100);
      };

      checkContent();
    });
  }

  /**
   * Set up message listener for communication with popup/background
   */
  setupMessageListener() {
    try {
      if (chrome?.runtime?.onMessage) {
        chrome.runtime.onMessage.addListener(this.handleMessage);
      }
    } catch (error) {
      this.logError('Failed to setup message listener', error);
    }
  }

  /**
   * Handle messages from popup/background script
   */
  handleMessage(request, _sender, sendResponse) {
    try {
      switch (request.action) {
        case 'ping':
          sendResponse({ success: true, initialized: this.isInitialized });
          return true;
        case 'togglePanel':
          this.togglePanel();
          sendResponse({ success: true });
          return true;
        case 'toggleGrid':
          this.toggleGrid();
          sendResponse({ success: true });
          return true;
        case 'forceShowGrid':
          this.forceShowGrid();
          sendResponse({ success: true });
          return true;
        case 'debugOverlay':
          this.debugOverlayState();
          sendResponse({ success: true, debug: this.getDebugInfo() });
          return true;
        default:
          sendResponse({ success: false, error: 'Unknown action' });
          return true;
      }
    } catch (error) {
      this.logError('Error handling message', error);
      sendResponse({ success: false, error: error.message });
      return true;
    }
  }

  /**
   * Detect if we're in a file context (viewing files directly)
   */
  detectFileContext() {
    try {
      const isFileURL = window.location.protocol === 'file:';
      const pathname = window.location.pathname.toLowerCase();

      // Decode URL to handle encoded characters like %20
      const decodedPathname = decodeURIComponent(pathname);

      const isImageFile = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(decodedPathname);
      const isVideoFile = /\.(mp4|webm|ogg|avi|mov|mkv|avi|wmv)$/i.test(decodedPathname);
      const isPDFFile = /\.pdf$/i.test(decodedPathname);
      const isTextFile = /\.(txt|md|json|xml|html|htm|css|js|ts)$/i.test(decodedPathname);

      // Safe DOM queries with additional checks
      const hasImage = this.safeQuerySelector('img');
      const hasVideo = this.safeQuerySelector('video');
      const hasPDF = this.safeQuerySelector('embed[type="application/pdf"]') ||
                     this.safeQuerySelector('object[type="application/pdf"]');

      // For file URLs, also check document title and body content
      const isFileViewer = isFileURL && (
        document.title.includes('file://') ||
        document.body.children.length <= 3 // Simple file viewer usually has minimal DOM
      );

      const hasFileViewerElements = hasImage || hasVideo || hasPDF;

      const result = {
        isFile: isFileURL || isImageFile || isVideoFile || isPDFFile || isTextFile || isFileViewer,
        isImage: isImageFile || (hasImage && (document.images.length === 1 || isFileURL)),
        isVideo: isVideoFile || (hasVideo && isFileURL),
        isPDF: isPDFFile || (hasPDF && isFileURL),
        isText: isTextFile,
        hasMedia: hasFileViewerElements,
        isFileURL: isFileURL,
        decodedPath: decodedPathname
      };

      // Log file context detection for debugging
      if (isFileURL) {
        console.log('[ArtistGridOverlay] File context detected:', result);
      }

      return result;
    } catch (error) {
      this.logError('Error detecting file context', error);
      return {
        isFile: false,
        isImage: false,
        isVideo: false,
        isPDF: false,
        isText: false,
        hasMedia: false,
        isFileURL: false,
        decodedPath: ''
      };
    }
  }

  /**
   * Main initialization method
   */
  async init() {
    if (this.isInitialized) return;

    try {
      // Wait for content to load if we're in a file context
      if (this.isFileContext?.isFile) {
        await this.waitForFileLoad();
      }

      // Load saved settings
      await this.loadSettings();

      // Create overlay elements
      this.createOverlay();
      this.createControlPanel();

      // Set up event listeners
      this.setupEventListeners();

      // Apply initial settings
      this.updateGrid();

      // Log initialization status for debugging
      console.log('[ArtistGridOverlay] Initialized successfully', {
        isFileContext: this.isFileContext,
        settings: this.settings,
        overlayExists: !!this.overlay,
        panelExists: !!this.panel
      });

      // Auto-show panel and grid for file contexts
      if (this.isFileContext?.isFile) {
        console.log('[ArtistGridOverlay] File context detected, will auto-show grid and panel');
        setTimeout(() => {
          // Auto-show grid for file contexts
          if (!this.settings.isVisible) {
            console.log('[ArtistGridOverlay] Auto-showing grid for file context');
            this.settings.isVisible = true;
            if (this.overlay) {
              this.overlay.classList.add('active');
              console.log('[ArtistGridOverlay] Grid overlay activated');
            }
            // Update toggle button text
            const toggleBtn = this.safeQuerySelector('#toggle-grid');
            if (toggleBtn) {
              toggleBtn.textContent = 'Hide Grid';
              toggleBtn.classList.add('active');
            }
            this.saveSettings();
          }

          // Auto-show panel
          if (!this.settings.panelVisible) {
            this.showPanel();
          }
        }, 1500);
      }

      this.isInitialized = true;
    } catch (error) {
      this.logError('Error during initialization', error);
      throw error;
    }
  }

  /**
   * Wait for file content to load with timeout
   */
  async waitForFileLoad() {
    return new Promise((resolve, reject) => {
      if (document.readyState === 'complete') {
        resolve();
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('File load timeout'));
      }, ArtistGridOverlay.CONSTANTS.FILE_LOAD_TIMEOUT);

      const checkLoad = () => {
        if (document.readyState === 'complete') {
          clearTimeout(timeout);
          // Additional wait for file content
          setTimeout(resolve, 500);
        } else {
          setTimeout(checkLoad, 100);
        }
      };

      checkLoad();
    });
  }

  /**
   * Utility methods
   */

  /**
   * Safe DOM query selector
   */
  safeQuerySelector(selector) {
    try {
      return document.querySelector(selector);
    } catch (error) {
      this.logError(`Error querying selector: ${selector}`, error);
      return null;
    }
  }

  /**
   * Safe DOM query selector all
   */
  safeQuerySelectorAll(selector) {
    try {
      return document.querySelectorAll(selector);
    } catch (error) {
      this.logError(`Error querying selector all: ${selector}`, error);
      return [];
    }
  }

  /**
   * Validate numeric input within range
   */
  validateNumericInput(value, min, max, defaultValue) {
    const num = parseFloat(value);
    if (isNaN(num)) return defaultValue;
    return Math.max(min, Math.min(max, num));
  }

  /**
   * Debounce function calls
   */
  debounce(key, func, delay = ArtistGridOverlay.CONSTANTS.DEBOUNCE_DELAY) {
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key));
    }

    const timer = setTimeout(() => {
      func();
      this.debounceTimers.delete(key);
    }, delay);

    this.debounceTimers.set(key, timer);
  }

  /**
   * Log errors with context
   */
  logError(message, error) {
    console.error(`[ArtistGridOverlay] ${message}:`, error);
  }

  /**
   * Retry async operations
   */
  async retryOperation(operation, maxAttempts = ArtistGridOverlay.CONSTANTS.RETRY_ATTEMPTS) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }
        await new Promise(resolve =>
          setTimeout(resolve, ArtistGridOverlay.CONSTANTS.RETRY_DELAY * attempt)
        );
      }
    }
  }

  /**
   * Load settings with validation and retry logic
   */
  async loadSettings() {
    try {
      const result = await this.retryOperation(async () => {
        if (!chrome?.storage?.sync) {
          throw new Error('Chrome storage API not available');
        }
        return await chrome.storage.sync.get(['artistGridSettings']);
      });

      if (result.artistGridSettings) {
        // Validate and merge settings
        const loadedSettings = this.validateSettings(result.artistGridSettings);
        this.settings = { ...this.settings, ...loadedSettings };
      }
    } catch (error) {
      this.logError('Failed to load settings, using defaults', error);
    }
  }

  /**
   * Save settings with retry logic
   */
  async saveSettings() {
    try {
      await this.retryOperation(async () => {
        if (!chrome?.storage?.sync) {
          throw new Error('Chrome storage API not available');
        }
        await chrome.storage.sync.set({ artistGridSettings: this.settings });
      });
    } catch (error) {
      this.logError('Failed to save settings', error);
    }
  }

  /**
   * Validate settings object
   */
  validateSettings(settings) {
    const validated = {};
    const defaults = this.getDefaultSettings();

    // Validate each setting
    validated.gridColor = this.validateColor(settings.gridColor) || defaults.gridColor;
    validated.gridSizeX = this.validateNumericInput(
      settings.gridSizeX,
      ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE,
      ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE,
      defaults.gridSizeX
    );
    validated.gridSizeY = this.validateNumericInput(
      settings.gridSizeY,
      ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE,
      ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE,
      defaults.gridSizeY
    );
    validated.gridOffsetX = this.validateNumericInput(
      settings.gridOffsetX,
      ArtistGridOverlay.CONSTANTS.MIN_OFFSET,
      ArtistGridOverlay.CONSTANTS.MAX_OFFSET,
      defaults.gridOffsetX
    );
    validated.gridOffsetY = this.validateNumericInput(
      settings.gridOffsetY,
      ArtistGridOverlay.CONSTANTS.MIN_OFFSET,
      ArtistGridOverlay.CONSTANTS.MAX_OFFSET,
      defaults.gridOffsetY
    );
    validated.opacity = this.validateNumericInput(
      settings.opacity,
      ArtistGridOverlay.CONSTANTS.MIN_OPACITY,
      ArtistGridOverlay.CONSTANTS.MAX_OPACITY,
      defaults.opacity
    );
    validated.isVisible = typeof settings.isVisible === 'boolean' ? settings.isVisible : defaults.isVisible;
    validated.panelVisible = typeof settings.panelVisible === 'boolean' ? settings.panelVisible : defaults.panelVisible;

    return validated;
  }

  /**
   * Validate color hex value
   */
  validateColor(color) {
    if (typeof color !== 'string') return null;
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexRegex.test(color) ? color : null;
  }

  /**
   * Create the grid overlay element with error handling
   */
  createOverlay() {
    try {
      // Remove existing overlay if present
      const existing = this.safeQuerySelector('#artist-grid-overlay');
      if (existing) {
        existing.remove();
      }

      const overlay = document.createElement('div');
      overlay.id = 'artist-grid-overlay';

      // Enhanced overlay for file contexts
      if (this.isFileContext?.isFile) {
        overlay.classList.add('file-context');
      }

      overlay.innerHTML = '<div class="grid-lines"></div>';

      if (this.settings.isVisible) {
        overlay.classList.add('active');
      }

      if (!document.body) {
        throw new Error('Document body not available');
      }

      document.body.appendChild(overlay);
      this.overlay = overlay;

      // Handle image-specific adjustments
      if (this.isFileContext?.isImage) {
        this.adjustForImageFile();
      }
    } catch (error) {
      this.logError('Failed to create overlay', error);
    }
  }

  /**
   * Adjust overlay for image file contexts
   */
  adjustForImageFile() {
    try {
      const img = this.safeQuerySelector('img');
      if (!img || !this.overlay) return;

      const adjustGrid = () => {
        try {
          const imgRect = img.getBoundingClientRect();
          if (imgRect.width > 0 && imgRect.height > 0) {
            // Image is visible, ensure overlay positioning
            this.overlay.style.position = 'fixed';
            this.overlay.style.top = '0';
            this.overlay.style.left = '0';
            this.overlay.style.width = '100vw';
            this.overlay.style.height = '100vh';
          }
        } catch (error) {
          this.logError('Error adjusting grid for image', error);
        }
      };

      // Adjust immediately and on resize
      adjustGrid();

      // Add resize listener with cleanup tracking
      const resizeListener = () => this.debounce('resize-adjust', adjustGrid, 100);
      window.addEventListener('resize', resizeListener);
      this.eventListeners.set('window-resize', { element: window, event: 'resize', listener: resizeListener });

      // Also adjust when image loads
      if (!img.complete) {
        const loadListener = adjustGrid;
        img.addEventListener('load', loadListener);
        this.eventListeners.set('img-load', { element: img, event: 'load', listener: loadListener });
      }
    } catch (error) {
      this.logError('Failed to adjust for image file', error);
    }
  }

  /**
   * Create the control panel with error handling
   */
  createControlPanel() {
    try {
      // Remove existing panel if present
      const existing = this.safeQuerySelector('#artist-grid-panel');
      if (existing) {
        existing.remove();
      }

      const panel = document.createElement('div');
      panel.id = 'artist-grid-panel';

      panel.innerHTML = this.generatePanelHTML();

      if (this.settings.panelVisible) {
        panel.classList.add('visible');
      }

      if (!document.body) {
        throw new Error('Document body not available');
      }

      document.body.appendChild(panel);
      this.panel = panel;
    } catch (error) {
      this.logError('Failed to create control panel', error);
    }
  }

  /**
   * Generate the HTML content for the control panel
   */
  generatePanelHTML() {
    const fileContextInfo = this.getFileContextInfo();
    const presetButtons = this.getPresetButtonsHTML();

    return `
      <div class="panel-header">
        <div class="panel-title">Artist Grid</div>
        <button class="close-btn" id="close-panel">×</button>
      </div>

      ${fileContextInfo}

      <div class="control-group">
        <label class="control-label">Grid Color</label>
        <div class="color-picker-wrapper">
          <input type="color" id="grid-color" value="${this.settings.gridColor}">
          <div class="color-preview" style="background-color: ${this.settings.gridColor}"></div>
          <span style="font-size: 12px;">${this.settings.gridColor}</span>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Grid Size X</label>
        <div class="control-row">
          <input type="range" id="grid-size-x" min="${ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE}" max="${ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE}" value="${this.settings.gridSizeX}">
          <input type="number" id="grid-size-x-num" min="${ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE}" max="${ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE}" value="${this.settings.gridSizeX}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Grid Size Y</label>
        <div class="control-row">
          <input type="range" id="grid-size-y" min="${ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE}" max="${ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE}" value="${this.settings.gridSizeY}">
          <input type="number" id="grid-size-y-num" min="${ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE}" max="${ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE}" value="${this.settings.gridSizeY}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Horizontal Offset</label>
        <div class="control-row">
          <input type="range" id="grid-offset-x" min="${ArtistGridOverlay.CONSTANTS.MIN_OFFSET}" max="${ArtistGridOverlay.CONSTANTS.MAX_OFFSET}" value="${this.settings.gridOffsetX}">
          <input type="number" id="grid-offset-x-num" min="${ArtistGridOverlay.CONSTANTS.MIN_OFFSET}" max="${ArtistGridOverlay.CONSTANTS.MAX_OFFSET}" value="${this.settings.gridOffsetX}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Vertical Offset</label>
        <div class="control-row">
          <input type="range" id="grid-offset-y" min="${ArtistGridOverlay.CONSTANTS.MIN_OFFSET}" max="${ArtistGridOverlay.CONSTANTS.MAX_OFFSET}" value="${this.settings.gridOffsetY}">
          <input type="number" id="grid-offset-y-num" min="${ArtistGridOverlay.CONSTANTS.MIN_OFFSET}" max="${ArtistGridOverlay.CONSTANTS.MAX_OFFSET}" value="${this.settings.gridOffsetY}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Opacity</label>
        <div class="opacity-control">
          <input type="range" id="opacity" min="${ArtistGridOverlay.CONSTANTS.MIN_OPACITY}" max="${ArtistGridOverlay.CONSTANTS.MAX_OPACITY}" step="0.1" value="${this.settings.opacity}">
          <span class="opacity-value">${Math.round(this.settings.opacity * 100)}%</span>
        </div>
      </div>

      ${presetButtons}

      <button class="toggle-grid" id="toggle-grid">
        ${this.settings.isVisible ? 'Hide Grid' : 'Show Grid'}
      </button>
    `;
  }

  /**
   * Get file context information HTML
   */
  getFileContextInfo() {
    if (!this.isFileContext?.isFile) return '';

    let icon = '📁 File';
    if (this.isFileContext.isImage) icon = '📷 Image File';
    else if (this.isFileContext.isVideo) icon = '🎥 Video File';
    else if (this.isFileContext.isPDF) icon = '📄 PDF File';

    return `<div class="file-context-indicator">${icon}</div>`;
  }

  /**
   * Get preset buttons HTML for file contexts
   */
  getPresetButtonsHTML() {
    if (!this.isFileContext?.isFile) return '';

    return `
      <div class="control-group">
        <button class="preset-btn" id="rule-of-thirds">Rule of Thirds</button>
        <button class="preset-btn" id="golden-ratio">Golden Ratio</button>
      </div>
    `;
  }

  /**
   * Set up all event listeners with error handling and cleanup tracking
   */
  setupEventListeners() {
    try {
      this.setupPanelDragging();
      this.setupPanelControls();
      this.setupGridControls();
      this.setupKeyboardShortcuts();
      this.setupPresetButtons();
    } catch (error) {
      this.logError('Failed to setup event listeners', error);
    }
  }

  /**
   * Set up panel dragging functionality
   */
  setupPanelDragging() {
    const header = this.safeQuerySelector('.panel-header');
    if (!header) return;

    const mouseDownHandler = (e) => this.startDrag(e);
    const mouseMoveHandler = (e) => this.drag(e);
    const mouseUpHandler = () => this.stopDrag();

    header.addEventListener('mousedown', mouseDownHandler);
    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);

    // Track listeners for cleanup
    this.eventListeners.set('panel-mousedown', { element: header, event: 'mousedown', listener: mouseDownHandler });
    this.eventListeners.set('document-mousemove', { element: document, event: 'mousemove', listener: mouseMoveHandler });
    this.eventListeners.set('document-mouseup', { element: document, event: 'mouseup', listener: mouseUpHandler });
  }

  /**
   * Set up panel control buttons
   */
  setupPanelControls() {
    // Close panel button
    const closeBtn = this.safeQuerySelector('#close-panel');
    if (closeBtn) {
      const closeHandler = () => {
        this.settings.panelVisible = false;
        this.panel.classList.remove('visible');
        this.debounce('save-settings', () => this.saveSettings());
      };
      closeBtn.addEventListener('click', closeHandler);
      this.eventListeners.set('close-panel', { element: closeBtn, event: 'click', listener: closeHandler });
    }

    // Toggle grid button
    const toggleBtn = this.safeQuerySelector('#toggle-grid');
    if (toggleBtn) {
      const toggleHandler = () => this.toggleGrid();
      toggleBtn.addEventListener('click', toggleHandler);
      this.eventListeners.set('toggle-grid', { element: toggleBtn, event: 'click', listener: toggleHandler });
    }
  }

  /**
   * Set up grid control inputs
   */
  setupGridControls() {
    // Color picker
    const colorPicker = this.safeQuerySelector('#grid-color');
    if (colorPicker) {
      const colorHandler = (e) => {
        const color = this.validateColor(e.target.value);
        if (color) {
          this.settings.gridColor = color;
          this.updateGrid();
          this.updateColorPreview();
          this.debounce('save-settings', () => this.saveSettings());
        }
      };
      colorPicker.addEventListener('input', colorHandler);
      this.eventListeners.set('color-picker', { element: colorPicker, event: 'input', listener: colorHandler });
    }

    // Grid size and offset controls
    this.setupRangeControl('grid-size-x', 'gridSizeX');
    this.setupRangeControl('grid-size-y', 'gridSizeY');
    this.setupRangeControl('grid-offset-x', 'gridOffsetX');
    this.setupRangeControl('grid-offset-y', 'gridOffsetY');

    // Opacity control
    const opacityRange = this.safeQuerySelector('#opacity');
    if (opacityRange) {
      const opacityHandler = (e) => {
        const opacity = this.validateNumericInput(
          e.target.value,
          ArtistGridOverlay.CONSTANTS.MIN_OPACITY,
          ArtistGridOverlay.CONSTANTS.MAX_OPACITY,
          this.settings.opacity
        );
        this.settings.opacity = opacity;
        this.updateGrid();
        this.updateOpacityDisplay();
        this.debounce('save-settings', () => this.saveSettings());
      };
      opacityRange.addEventListener('input', opacityHandler);
      this.eventListeners.set('opacity-range', { element: opacityRange, event: 'input', listener: opacityHandler });
    }
  }

  /**
   * Set up keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    const keydownHandler = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'G') {
        e.preventDefault();
        this.togglePanel();
      }
    };
    document.addEventListener('keydown', keydownHandler);
    this.eventListeners.set('document-keydown', { element: document, event: 'keydown', listener: keydownHandler });
  }

  /**
   * Set up preset buttons for file contexts
   */
  setupPresetButtons() {
    if (!this.isFileContext?.isFile) return;

    const ruleOfThirdsBtn = this.safeQuerySelector('#rule-of-thirds');
    if (ruleOfThirdsBtn) {
      const ruleHandler = () => this.applyRuleOfThirds();
      ruleOfThirdsBtn.addEventListener('click', ruleHandler);
      this.eventListeners.set('rule-of-thirds', { element: ruleOfThirdsBtn, event: 'click', listener: ruleHandler });
    }

    const goldenRatioBtn = this.safeQuerySelector('#golden-ratio');
    if (goldenRatioBtn) {
      const goldenHandler = () => this.applyGoldenRatio();
      goldenRatioBtn.addEventListener('click', goldenHandler);
      this.eventListeners.set('golden-ratio', { element: goldenRatioBtn, event: 'click', listener: goldenHandler });
    }
  }

  /**
   * Set up range control with validation and error handling
   */
  setupRangeControl(id, settingKey) {
    const range = this.safeQuerySelector(`#${id}`);
    const number = this.safeQuerySelector(`#${id}-num`);

    if (!range || !number) {
      this.logError(`Range control elements not found for ${id}`);
      return;
    }

    // Determine validation range based on setting type
    let min, max;
    if (settingKey.includes('Size')) {
      min = ArtistGridOverlay.CONSTANTS.MIN_GRID_SIZE;
      max = ArtistGridOverlay.CONSTANTS.MAX_GRID_SIZE;
    } else if (settingKey.includes('Offset')) {
      min = ArtistGridOverlay.CONSTANTS.MIN_OFFSET;
      max = ArtistGridOverlay.CONSTANTS.MAX_OFFSET;
    } else {
      min = 0;
      max = 1000;
    }

    const updateSetting = (value) => {
      const validatedValue = this.validateNumericInput(value, min, max, this.settings[settingKey]);
      this.settings[settingKey] = validatedValue;

      // Update both controls to ensure sync
      range.value = validatedValue;
      number.value = validatedValue;

      this.updateGrid();
      this.debounce('save-settings', () => this.saveSettings());
    };

    const rangeHandler = (e) => updateSetting(parseInt(e.target.value));
    const numberHandler = (e) => updateSetting(parseInt(e.target.value));

    range.addEventListener('input', rangeHandler);
    number.addEventListener('input', numberHandler);

    // Track listeners for cleanup
    this.eventListeners.set(`${id}-range`, { element: range, event: 'input', listener: rangeHandler });
    this.eventListeners.set(`${id}-number`, { element: number, event: 'input', listener: numberHandler });
  }

  /**
   * Set up cleanup handlers for page unload
   */
  setupCleanupHandlers() {
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    this.eventListeners.set('window-beforeunload', {
      element: window,
      event: 'beforeunload',
      listener: this.handleBeforeUnload
    });
  }

  /**
   * Handle page unload cleanup
   */
  handleBeforeUnload() {
    this.cleanup();
  }

  /**
   * Clean up event listeners and timers
   */
  cleanup() {
    try {
      // Clear all debounce timers
      this.debounceTimers.forEach(timer => clearTimeout(timer));
      this.debounceTimers.clear();

      // Remove all event listeners
      this.eventListeners.forEach(({ element, event, listener }) => {
        try {
          element.removeEventListener(event, listener);
        } catch (error) {
          this.logError(`Failed to remove event listener for ${event}`, error);
        }
      });
      this.eventListeners.clear();

      // Remove Chrome message listener
      if (chrome?.runtime?.onMessage) {
        try {
          chrome.runtime.onMessage.removeListener(this.handleMessage);
        } catch (error) {
          this.logError('Failed to remove Chrome message listener', error);
        }
      }
    } catch (error) {
      this.logError('Error during cleanup', error);
    }
  }

  /**
   * Start dragging the panel
   */
  startDrag(e) {
    if (!this.panel) return;

    this.isDragging = true;
    this.dragOffset.x = e.clientX - this.panel.offsetLeft;
    this.dragOffset.y = e.clientY - this.panel.offsetTop;
    this.panel.style.cursor = 'grabbing';
  }

  /**
   * Handle panel dragging
   */
  drag(e) {
    if (!this.isDragging || !this.panel) return;

    e.preventDefault();
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;

    // Keep panel within viewport
    const maxX = window.innerWidth - this.panel.offsetWidth;
    const maxY = window.innerHeight - this.panel.offsetHeight;

    this.panel.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
    this.panel.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
    this.panel.style.right = 'auto';
  }

  /**
   * Stop dragging the panel
   */
  stopDrag() {
    this.isDragging = false;
    if (this.panel) {
      this.panel.style.cursor = 'default';
    }
  }

  /**
   * Update the grid display with current settings
   */
  updateGrid() {
    try {
      if (!this.overlay) return;

      const gridLines = this.overlay.querySelector('.grid-lines');
      if (!gridLines) {
        this.logError('Grid lines element not found');
        return;
      }

      gridLines.style.setProperty('--grid-color', this.settings.gridColor);
      gridLines.style.setProperty('--grid-size-x', this.settings.gridSizeX + 'px');
      gridLines.style.setProperty('--grid-size-y', this.settings.gridSizeY + 'px');
      gridLines.style.setProperty('--grid-offset-x', this.settings.gridOffsetX + 'px');
      gridLines.style.setProperty('--grid-offset-y', this.settings.gridOffsetY + 'px');

      this.overlay.style.opacity = this.settings.opacity;
    } catch (error) {
      this.logError('Failed to update grid', error);
    }
  }

  /**
   * Update the color preview in the panel
   */
  updateColorPreview() {
    try {
      if (!this.panel) return;

      const preview = this.panel.querySelector('.color-preview');
      const colorText = this.panel.querySelector('.color-picker-wrapper span');

      if (preview) {
        preview.style.backgroundColor = this.settings.gridColor;
      }

      if (colorText) {
        colorText.textContent = this.settings.gridColor;
      }
    } catch (error) {
      this.logError('Failed to update color preview', error);
    }
  }

  /**
   * Update the opacity display in the panel
   */
  updateOpacityDisplay() {
    try {
      if (!this.panel) return;

      const opacityValue = this.panel.querySelector('.opacity-value');
      if (opacityValue) {
        opacityValue.textContent = Math.round(this.settings.opacity * 100) + '%';
      }
    } catch (error) {
      this.logError('Failed to update opacity display', error);
    }
  }

  /**
   * Toggle grid visibility
   */
  toggleGrid() {
    try {
      this.settings.isVisible = !this.settings.isVisible;

      console.log('[ArtistGridOverlay] Toggling grid visibility:', {
        isVisible: this.settings.isVisible,
        overlayExists: !!this.overlay,
        isFileContext: this.isFileContext?.isFile
      });

      if (this.overlay) {
        if (this.settings.isVisible) {
          this.overlay.classList.add('active');
          console.log('[ArtistGridOverlay] Grid activated, classes:', this.overlay.className);
        } else {
          this.overlay.classList.remove('active');
          console.log('[ArtistGridOverlay] Grid deactivated');
        }
      } else {
        console.warn('[ArtistGridOverlay] No overlay element found!');
      }

      const toggleBtn = this.safeQuerySelector('#toggle-grid');
      if (toggleBtn) {
        toggleBtn.textContent = this.settings.isVisible ? 'Hide Grid' : 'Show Grid';
        toggleBtn.classList.toggle('active', this.settings.isVisible);
      }

      this.debounce('save-settings', () => this.saveSettings());

      // Debug: Log current overlay state
      this.debugOverlayState();
    } catch (error) {
      this.logError('Failed to toggle grid', error);
    }
  }

  /**
   * Force show grid (for debugging)
   */
  forceShowGrid() {
    try {
      console.log('[ArtistGridOverlay] Force showing grid');

      this.settings.isVisible = true;

      if (!this.overlay) {
        console.log('[ArtistGridOverlay] No overlay found, creating one');
        this.createOverlay();
      }

      if (this.overlay) {
        this.overlay.classList.add('active');
        if (this.isFileContext?.isFile) {
          this.overlay.classList.add('file-context');
        }
        console.log('[ArtistGridOverlay] Grid forced visible, classes:', this.overlay.className);
      }

      this.updateGrid();
      this.debugOverlayState();
      this.saveSettings();
    } catch (error) {
      this.logError('Failed to force show grid', error);
    }
  }

  /**
   * Get debug information
   */
  getDebugInfo() {
    const info = {
      isInitialized: this.isInitialized,
      isFileContext: this.isFileContext,
      settings: this.settings,
      overlayExists: !!this.overlay,
      panelExists: !!this.panel,
      url: window.location.href,
      protocol: window.location.protocol,
      pathname: window.location.pathname
    };

    if (this.overlay) {
      const rect = this.overlay.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(this.overlay);
      info.overlayInfo = {
        classes: this.overlay.className,
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
        zIndex: computedStyle.zIndex,
        position: computedStyle.position,
        dimensions: { width: rect.width, height: rect.height },
        location: { top: rect.top, left: rect.left }
      };
    }

    return info;
  }

  /**
   * Debug method to log overlay state
   */
  debugOverlayState() {
    const debugInfo = this.getDebugInfo();
    console.log('[ArtistGridOverlay] Debug info:', debugInfo);
  }

  /**
   * Toggle panel visibility
   */
  togglePanel() {
    try {
      this.settings.panelVisible = !this.settings.panelVisible;

      if (this.panel) {
        if (this.settings.panelVisible) {
          this.panel.classList.add('visible');
        } else {
          this.panel.classList.remove('visible');
        }
      }

      this.debounce('save-settings', () => this.saveSettings());
    } catch (error) {
      this.logError('Failed to toggle panel', error);
    }
  }

  /**
   * Show the control panel
   */
  showPanel() {
    try {
      this.settings.panelVisible = true;
      if (this.panel) {
        this.panel.classList.add('visible');
      }
      this.debounce('save-settings', () => this.saveSettings());
    } catch (error) {
      this.logError('Failed to show panel', error);
    }
  }
  
  /**
   * Apply rule of thirds grid preset
   */
  applyRuleOfThirds() {
    try {
      // Calculate viewport dimensions for rule of thirds
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Rule of thirds: divide into 3x3 grid
      this.settings.gridSizeX = Math.round(viewportWidth / 3);
      this.settings.gridSizeY = Math.round(viewportHeight / 3);
      this.settings.gridOffsetX = 0;
      this.settings.gridOffsetY = 0;
      this.settings.gridColor = '#ff0000';
      this.settings.opacity = 0.6;

      this.updateControlValues();
      this.updateGrid();
      this.debounce('save-settings', () => this.saveSettings());

      if (!this.settings.isVisible) {
        this.toggleGrid();
      }
    } catch (error) {
      this.logError('Failed to apply rule of thirds', error);
    }
  }

  /**
   * Apply golden ratio grid preset
   */
  applyGoldenRatio() {
    try {
      // Golden ratio: approximately 1.618
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const goldenRatio = 1.618;

      // Apply golden ratio proportions
      this.settings.gridSizeX = Math.round(viewportWidth / goldenRatio);
      this.settings.gridSizeY = Math.round(viewportHeight / goldenRatio);
      this.settings.gridOffsetX = 0;
      this.settings.gridOffsetY = 0;
      this.settings.gridColor = '#ffd700';
      this.settings.opacity = 0.5;

      this.updateControlValues();
      this.updateGrid();
      this.debounce('save-settings', () => this.saveSettings());

      if (!this.settings.isVisible) {
        this.toggleGrid();
      }
    } catch (error) {
      this.logError('Failed to apply golden ratio', error);
    }
  }

  /**
   * Update all control elements with current settings
   */
  updateControlValues() {
    try {
      const controls = [
        { id: 'grid-color', value: this.settings.gridColor },
        { id: 'grid-size-x', value: this.settings.gridSizeX },
        { id: 'grid-size-x-num', value: this.settings.gridSizeX },
        { id: 'grid-size-y', value: this.settings.gridSizeY },
        { id: 'grid-size-y-num', value: this.settings.gridSizeY },
        { id: 'grid-offset-x', value: this.settings.gridOffsetX },
        { id: 'grid-offset-x-num', value: this.settings.gridOffsetX },
        { id: 'grid-offset-y', value: this.settings.gridOffsetY },
        { id: 'grid-offset-y-num', value: this.settings.gridOffsetY },
        { id: 'opacity', value: this.settings.opacity }
      ];

      controls.forEach(({ id, value }) => {
        const element = this.safeQuerySelector(`#${id}`);
        if (element) {
          element.value = value;
        }
      });

      this.updateColorPreview();
      this.updateOpacityDisplay();
    } catch (error) {
      this.logError('Failed to update control values', error);
    }
  }
}

/**
 * Safe initialization with error handling
 */
function initializeArtistGridOverlay() {
  try {
    // Prevent multiple instances
    if (window.artistGridOverlayInstance) {
      return;
    }

    window.artistGridOverlayInstance = new ArtistGridOverlay();
  } catch (error) {
    console.error('[ArtistGridOverlay] Failed to initialize:', error);
  }
}

// Initialize the overlay when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeArtistGridOverlay);
} else {
  initializeArtistGridOverlay();
}