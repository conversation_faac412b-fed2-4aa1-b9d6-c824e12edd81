// Artist Grid Overlay - Content Script (Enhanced for File Support)

class ArtistGridOverlay {
  constructor() {
    this.isInitialized = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.isFileContext = this.detectFileContext();
    this.settings = {
      gridColor: '#ff0000',
      gridSizeX: 50,
      gridSizeY: 50,
      gridOffsetX: 0,
      gridOffsetY: 0,
      opacity: 0.7,
      isVisible: false,
      panelVisible: false
    };
    
    this.init();
    this.setupMessageListener();
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'ping') {
        sendResponse(true);
        return true;
      }
      if (request.action === 'togglePanel') {
        this.togglePanel();
        sendResponse(true);
        return true;
      }
      if (request.action === 'toggleGrid') {
        this.toggleGrid();
        sendResponse(true);
        return true;
      }
    });
  }

  detectFileContext() {
    // Check if we're viewing a file directly
    const isFileURL = window.location.protocol === 'file:';
    const isImageFile = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(window.location.pathname);
    const isVideoFile = /\.(mp4|webm|ogg|avi|mov)$/i.test(window.location.pathname);
    const isPDFFile = /\.pdf$/i.test(window.location.pathname);
    const hasFileViewerElements = document.querySelector('img') || document.querySelector('video') || document.querySelector('embed[type="application/pdf"]');
    
    return {
      isFile: isFileURL || isImageFile || isVideoFile || isPDFFile,
      isImage: isImageFile || (document.querySelector('img') && document.images.length === 1),
      isVideo: isVideoFile || document.querySelector('video'),
      isPDF: isPDFFile || document.querySelector('embed[type="application/pdf"]'),
      hasMedia: hasFileViewerElements
    };
  }

  async init() {
    if (this.isInitialized) return;
    
    // Wait for content to load if we're in a file context
    if (this.isFileContext.isFile) {
      await this.waitForFileLoad();
    }
    
    // Load saved settings
    await this.loadSettings();
    
    // Create overlay elements
    this.createOverlay();
    this.createControlPanel();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Apply initial settings
    this.updateGrid();
    
    // Auto-show panel for file contexts
    if (this.isFileContext.isFile && !this.settings.panelVisible) {
      setTimeout(() => {
        this.showPanel();
      }, 1000);
    }
    
    this.isInitialized = true;
  }

  async waitForFileLoad() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
        return;
      }
      
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          // Additional wait for file content
          setTimeout(resolve, 500);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      
      checkLoad();
    });
  }
    
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['artistGridSettings']);
      if (result.artistGridSettings) {
        this.settings = { ...this.settings, ...result.artistGridSettings };
      }
    } catch (error) {
      console.log('Using default settings');
    }
  }

  async saveSettings() {
    try {
      await chrome.storage.sync.set({ artistGridSettings: this.settings });
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  createOverlay() {
    // Remove existing overlay if present
    const existing = document.getElementById('artist-grid-overlay');
    if (existing) existing.remove();

    const overlay = document.createElement('div');
    overlay.id = 'artist-grid-overlay';
    
    // Enhanced overlay for file contexts
    if (this.isFileContext.isFile) {
      overlay.classList.add('file-context');
    }
    
    overlay.innerHTML = '<div class="grid-lines"></div>';
    
    if (this.settings.isVisible) {
      overlay.classList.add('active');
    }

    document.body.appendChild(overlay);
    this.overlay = overlay;
    
    // Handle image-specific adjustments
    if (this.isFileContext.isImage) {
      this.adjustForImageFile();
    }
  }

  adjustForImageFile() {
    // Find the main image element
    const img = document.querySelector('img');
    if (img) {
      // Ensure the grid covers the image properly
      const adjustGrid = () => {
        const imgRect = img.getBoundingClientRect();
        if (imgRect.width > 0 && imgRect.height > 0) {
          // Image is visible, ensure overlay positioning
          this.overlay.style.position = 'fixed';
          this.overlay.style.top = '0';
          this.overlay.style.left = '0';
          this.overlay.style.width = '100vw';
          this.overlay.style.height = '100vh';
        }
      };
      
      // Adjust immediately and on resize
      adjustGrid();
      window.addEventListener('resize', adjustGrid);
      
      // Also adjust when image loads
      if (!img.complete) {
        img.addEventListener('load', adjustGrid);
      }
    }
  }

  createControlPanel() {
    // Remove existing panel if present
    const existing = document.getElementById('artist-grid-panel');
    if (existing) existing.remove();

    const panel = document.createElement('div');
    panel.id = 'artist-grid-panel';
    
    // Add file context indicator
    const fileContextInfo = this.isFileContext.isFile ? 
      `<div class="file-context-indicator">
        ${this.isFileContext.isImage ? '📷 Image File' : 
          this.isFileContext.isVideo ? '🎥 Video File' : 
          this.isFileContext.isPDF ? '📄 PDF File' : '📁 File'}
      </div>` : '';
    
    panel.innerHTML = `
      <div class="panel-header">
        <div class="panel-title">Artist Grid</div>
        <button class="close-btn" id="close-panel">×</button>
      </div>
      
      ${fileContextInfo}
      
      <div class="control-group">
        <label class="control-label">Grid Color</label>
        <div class="color-picker-wrapper">
          <input type="color" id="grid-color" value="${this.settings.gridColor}">
          <div class="color-preview" style="background-color: ${this.settings.gridColor}"></div>
          <span style="font-size: 12px;">${this.settings.gridColor}</span>
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Grid Size X</label>
        <div class="control-row">
          <input type="range" id="grid-size-x" min="10" max="200" value="${this.settings.gridSizeX}">
          <input type="number" id="grid-size-x-num" min="10" max="200" value="${this.settings.gridSizeX}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Grid Size Y</label>
        <div class="control-row">
          <input type="range" id="grid-size-y" min="10" max="200" value="${this.settings.gridSizeY}">
          <input type="number" id="grid-size-y-num" min="10" max="200" value="${this.settings.gridSizeY}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Horizontal Offset</label>
        <div class="control-row">
          <input type="range" id="grid-offset-x" min="-100" max="100" value="${this.settings.gridOffsetX}">
          <input type="number" id="grid-offset-x-num" min="-100" max="100" value="${this.settings.gridOffsetX}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Vertical Offset</label>
        <div class="control-row">
          <input type="range" id="grid-offset-y" min="-100" max="100" value="${this.settings.gridOffsetY}">
          <input type="number" id="grid-offset-y-num" min="-100" max="100" value="${this.settings.gridOffsetY}">
        </div>
      </div>

      <div class="control-group">
        <label class="control-label">Opacity</label>
        <div class="opacity-control">
          <input type="range" id="opacity" min="0.1" max="1" step="0.1" value="${this.settings.opacity}">
          <span class="opacity-value">${Math.round(this.settings.opacity * 100)}%</span>
        </div>
      </div>
      
      ${this.isFileContext.isFile ? `
      <div class="control-group">
        <button class="preset-btn" id="rule-of-thirds">Rule of Thirds</button>
        <button class="preset-btn" id="golden-ratio">Golden Ratio</button>
      </div>
      ` : ''}

      <button class="toggle-grid" id="toggle-grid">
        ${this.settings.isVisible ? 'Hide Grid' : 'Show Grid'}
      </button>
    `;

    if (this.settings.panelVisible) {
      panel.classList.add('visible');
    }

    document.body.appendChild(panel);
    this.panel = panel;
  }

  setupEventListeners() {
    // Panel dragging
    const header = this.panel.querySelector('.panel-header');
    header.addEventListener('mousedown', (e) => this.startDrag(e));
    document.addEventListener('mousemove', (e) => this.drag(e));
    document.addEventListener('mouseup', () => this.stopDrag());

    // Close panel
    document.getElementById('close-panel').addEventListener('click', () => {
      this.settings.panelVisible = false;
      this.panel.classList.remove('visible');
      this.saveSettings();
    });

    // Toggle grid
    document.getElementById('toggle-grid').addEventListener('click', () => {
      this.toggleGrid();
    });

    // Color picker
    const colorPicker = document.getElementById('grid-color');
    colorPicker.addEventListener('input', (e) => {
      this.settings.gridColor = e.target.value;
      this.updateGrid();
      this.updateColorPreview();
      this.saveSettings();
    });

    // Grid size controls
    this.setupRangeControl('grid-size-x', 'gridSizeX');
    this.setupRangeControl('grid-size-y', 'gridSizeY');
    this.setupRangeControl('grid-offset-x', 'gridOffsetX');
    this.setupRangeControl('grid-offset-y', 'gridOffsetY');

    // Opacity control
    const opacityRange = document.getElementById('opacity');
    opacityRange.addEventListener('input', (e) => {
      this.settings.opacity = parseFloat(e.target.value);
      this.updateGrid();
      this.updateOpacityDisplay();
      this.saveSettings();
    });

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'togglePanel') {
        this.togglePanel();
        sendResponse({ success: true });
      } else if (request.action === 'toggleGrid') {
        this.toggleGrid();
        sendResponse({ success: true });
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'G') {
        e.preventDefault();
        this.togglePanel();
      }
    });
    
    // File-specific preset buttons
    if (this.isFileContext.isFile) {
      const ruleOfThirdsBtn = document.getElementById('rule-of-thirds');
      const goldenRatioBtn = document.getElementById('golden-ratio');
      
      if (ruleOfThirdsBtn) {
        ruleOfThirdsBtn.addEventListener('click', () => this.applyRuleOfThirds());
      }
      
      if (goldenRatioBtn) {
        goldenRatioBtn.addEventListener('click', () => this.applyGoldenRatio());
      }
    }
  }

  setupRangeControl(id, settingKey) {
    const range = document.getElementById(id);
    const number = document.getElementById(id + '-num');

    range.addEventListener('input', (e) => {
      const value = parseInt(e.target.value);
      this.settings[settingKey] = value;
      number.value = value;
      this.updateGrid();
      this.saveSettings();
    });

    number.addEventListener('input', (e) => {
      const value = parseInt(e.target.value);
      this.settings[settingKey] = value;
      range.value = value;
      this.updateGrid();
      this.saveSettings();
    });
  }

  startDrag(e) {
    this.isDragging = true;
    this.dragOffset.x = e.clientX - this.panel.offsetLeft;
    this.dragOffset.y = e.clientY - this.panel.offsetTop;
    this.panel.style.cursor = 'grabbing';
  }

  drag(e) {
    if (!this.isDragging) return;
    
    e.preventDefault();
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;
    
    // Keep panel within viewport
    const maxX = window.innerWidth - this.panel.offsetWidth;
    const maxY = window.innerHeight - this.panel.offsetHeight;
    
    this.panel.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
    this.panel.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
    this.panel.style.right = 'auto';
  }

  stopDrag() {
    this.isDragging = false;
    if (this.panel) {
      this.panel.style.cursor = 'default';
    }
  }

  updateGrid() {
    if (!this.overlay) return;

    const gridLines = this.overlay.querySelector('.grid-lines');
    gridLines.style.setProperty('--grid-color', this.settings.gridColor);
    gridLines.style.setProperty('--grid-size-x', this.settings.gridSizeX + 'px');
    gridLines.style.setProperty('--grid-size-y', this.settings.gridSizeY + 'px');
    gridLines.style.setProperty('--grid-offset-x', this.settings.gridOffsetX + 'px');
    gridLines.style.setProperty('--grid-offset-y', this.settings.gridOffsetY + 'px');
    
    this.overlay.style.opacity = this.settings.opacity;
  }

  updateColorPreview() {
    const preview = this.panel.querySelector('.color-preview');
    const colorText = this.panel.querySelector('.color-picker-wrapper span');
    if (preview && colorText) {
      preview.style.backgroundColor = this.settings.gridColor;
      colorText.textContent = this.settings.gridColor;
    }
  }

  updateOpacityDisplay() {
    const opacityValue = this.panel.querySelector('.opacity-value');
    if (opacityValue) {
      opacityValue.textContent = Math.round(this.settings.opacity * 100) + '%';
    }
  }

  toggleGrid() {
    this.settings.isVisible = !this.settings.isVisible;
    
    if (this.settings.isVisible) {
      this.overlay.classList.add('active');
    } else {
      this.overlay.classList.remove('active');
    }

    const toggleBtn = document.getElementById('toggle-grid');
    toggleBtn.textContent = this.settings.isVisible ? 'Hide Grid' : 'Show Grid';
    toggleBtn.classList.toggle('active', this.settings.isVisible);

    this.saveSettings();
  }

  togglePanel() {
    this.settings.panelVisible = !this.settings.panelVisible;
    
    if (this.settings.panelVisible) {
      this.panel.classList.add('visible');
    } else {
      this.panel.classList.remove('visible');
    }

    this.saveSettings();
  }

  showPanel() {
    this.settings.panelVisible = true;
    this.panel.classList.add('visible');
    this.saveSettings();
  }
  
  applyRuleOfThirds() {
    // Calculate viewport dimensions for rule of thirds
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Rule of thirds: divide into 3x3 grid
    this.settings.gridSizeX = Math.round(viewportWidth / 3);
    this.settings.gridSizeY = Math.round(viewportHeight / 3);
    this.settings.gridOffsetX = 0;
    this.settings.gridOffsetY = 0;
    this.settings.gridColor = '#ff0000';
    this.settings.opacity = 0.6;
    
    this.updateControlValues();
    this.updateGrid();
    this.saveSettings();
    
    if (!this.settings.isVisible) {
      this.toggleGrid();
    }
  }
  
  applyGoldenRatio() {
    // Golden ratio: approximately 1.618
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const goldenRatio = 1.618;
    
    // Apply golden ratio proportions
    this.settings.gridSizeX = Math.round(viewportWidth / goldenRatio);
    this.settings.gridSizeY = Math.round(viewportHeight / goldenRatio);
    this.settings.gridOffsetX = 0;
    this.settings.gridOffsetY = 0;
    this.settings.gridColor = '#ffd700';
    this.settings.opacity = 0.5;
    
    this.updateControlValues();
    this.updateGrid();
    this.saveSettings();
    
    if (!this.settings.isVisible) {
      this.toggleGrid();
    }
  }
  
  updateControlValues() {
    // Update all control elements with current settings
    document.getElementById('grid-color').value = this.settings.gridColor;
    document.getElementById('grid-size-x').value = this.settings.gridSizeX;
    document.getElementById('grid-size-x-num').value = this.settings.gridSizeX;
    document.getElementById('grid-size-y').value = this.settings.gridSizeY;
    document.getElementById('grid-size-y-num').value = this.settings.gridSizeY;
    document.getElementById('grid-offset-x').value = this.settings.gridOffsetX;
    document.getElementById('grid-offset-x-num').value = this.settings.gridOffsetX;
    document.getElementById('grid-offset-y').value = this.settings.gridOffsetY;
    document.getElementById('grid-offset-y-num').value = this.settings.gridOffsetY;
    document.getElementById('opacity').value = this.settings.opacity;
    
    this.updateColorPreview();
    this.updateOpacityDisplay();
  }
}

// Initialize the overlay when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ArtistGridOverlay();
  });
} else {
  new ArtistGridOverlay();
}